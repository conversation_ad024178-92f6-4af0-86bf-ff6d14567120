use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::Mutex;
use tracing::{debug, info, warn, error};
use kalosm::language::*;

/// Kalosm-compatible ingredient structure for structured generation
#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize)]
pub struct KalosmIngredient {
    /// The ingredient name (e.g., "all-purpose flour", "chicken breast")
    pub name: String,

    /// The amount as a decimal number (e.g., 2.5, 0.25, 1.0)
    pub amount: f64,

    /// The unit of measurement (e.g., "cup", "tbsp", "lb", "oz", "unit", "")
    pub unit: String,

    /// Optional section for grouped ingredients (e.g., "Cake Layer", "Frosting")
    pub section: Option<String>,
}

/// Ingredient parsing service using Kalosm for local AI inference
pub struct IngredientParser {
    /// The Kalosm language model for structured generation
    model: Arc<Mutex<Option<Llama>>>,
}

impl IngredientParser {
    /// Create a new ingredient parser instance
    pub fn new() -> Self {
        Self {
            model: Arc::new(Mutex::new(None)),
        }
    }

    /// Initialize the Kalosm model (lazy loading)
    async fn ensure_model_loaded(&self) -> Result<()> {
        let mut model_guard = self.model.lock().await;

        if model_guard.is_none() {
            info!("Loading Kalosm Phi-3 model for ingredient parsing...");

            // Use Phi-3 model which is lightweight and good for structured generation
            // This is a good balance between size (~2.4GB) and performance
            match Llama::phi_3().await {
                Ok(model) => {
                    info!("Successfully loaded Kalosm Phi-3 model");
                    *model_guard = Some(model);
                }
                Err(e) => {
                    error!("Failed to load Kalosm model: {}", e);
                    return Err(e.into());
                }
            }
        }

        Ok(())
    }

    /// Parse a single ingredient string using Kalosm structured generation
    pub async fn parse_ingredient(
        &self,
        ingredient_text: &str,
        section: Option<String>,
    ) -> Result<Option<crate::database::Ingredient>> {
        // Skip empty or invalid ingredients
        if ingredient_text.trim().is_empty() {
            return Ok(None);
        }

        debug!("Parsing ingredient with Kalosm: '{}'", ingredient_text);

        // Try Kalosm parsing first
        match self.kalosm_parse(ingredient_text, section.clone()).await {
            Ok(Some(ingredient)) => {
                debug!("Successfully parsed with Kalosm: {:?}", ingredient);
                Ok(Some(ingredient))
            }
            Ok(None) => {
                debug!("Kalosm returned None for ingredient: '{}'", ingredient_text);
                Ok(None)
            }
            Err(e) => {
                warn!("Kalosm parsing failed for '{}': {}, using fallback", ingredient_text, e);
                // Fallback to regex parsing
                self.fallback_parse(ingredient_text, section)
            }
        }
    }

    /// Parse ingredient using Kalosm text generation
    async fn kalosm_parse(
        &self,
        ingredient_text: &str,
        section: Option<String>,
    ) -> Result<Option<crate::database::Ingredient>> {
        // Ensure model is loaded
        self.ensure_model_loaded().await?;

        let model_guard = self.model.lock().await;
        let model = model_guard.as_ref().ok_or_else(|| {
            anyhow::anyhow!("Model not loaded")
        })?;

        // Create a prompt for ingredient parsing
        let prompt = format!(
            "Parse the following ingredient into JSON format with fields: name (string), amount (number), unit (string). If no amount is specified, use 1.0. If no unit is specified, use 'unit'. Return only valid JSON without any explanation.\n\nIngredient: \"{}\"\n\nJSON:",
            ingredient_text
        );

        debug!("Kalosm prompt: {}", prompt);

        // Use task-based generation for better consistency
        let task = model.task("You are an expert ingredient parser. Parse ingredients into JSON format with name, amount, and unit fields. Return only valid JSON.");

        match task(&prompt).await {
            Ok(response) => {
                debug!("Kalosm response: {}", response);

                // Try to parse the JSON response
                match self.parse_kalosm_response(&response, section) {
                    Ok(Some(ingredient)) => Ok(Some(ingredient)),
                    Ok(None) => {
                        debug!("Failed to parse Kalosm JSON response");
                        Err(anyhow::anyhow!("Invalid JSON response from Kalosm"))
                    }
                    Err(e) => {
                        debug!("Error parsing Kalosm response: {}", e);
                        Err(e)
                    }
                }
            }
            Err(e) => {
                debug!("Kalosm text generation failed: {}", e);
                Err(e.into())
            }
        }
    }

    /// Parse the JSON response from Kalosm
    fn parse_kalosm_response(
        &self,
        response: &str,
        section: Option<String>,
    ) -> Result<Option<crate::database::Ingredient>> {
        // Clean up the response - remove any markdown formatting or extra text
        let json_str = response
            .trim()
            .trim_start_matches("```json")
            .trim_start_matches("```")
            .trim_end_matches("```")
            .trim();

        // Try to parse as JSON
        match serde_json::from_str::<KalosmIngredient>(json_str) {
            Ok(kalosm_ingredient) => {
                // Convert to database ingredient
                let db_ingredient = crate::database::Ingredient {
                    name: kalosm_ingredient.name.trim().to_string(),
                    amount: kalosm_ingredient.amount.to_string(),
                    unit: normalize_unit(&kalosm_ingredient.unit),
                    category: None,
                    section: section.or(kalosm_ingredient.section),
                };

                // Validate the parsed ingredient
                if db_ingredient.name.is_empty() {
                    debug!("Kalosm returned empty ingredient name");
                    return Ok(None);
                }

                Ok(Some(db_ingredient))
            }
            Err(e) => {
                debug!("Failed to parse JSON response: {} - Response: {}", e, json_str);
                Err(anyhow::anyhow!("Invalid JSON format: {}", e))
            }
        }
    }

    /// Fallback to existing regex-based parsing
    fn fallback_parse(
        &self,
        ingredient_text: &str,
        section: Option<String>,
    ) -> Result<Option<crate::database::Ingredient>> {
        // Simple parsing logic for now - can be enhanced later
        let trimmed = ingredient_text.trim();

        if trimmed.is_empty() {
            return Ok(None);
        }

        // Basic regex pattern to extract amount, unit, and name
        let pattern = r"^(\d+(?:\.\d+)?(?:\s*[¼½¾⅓⅔⅛⅜⅝⅞])?)\s*([a-zA-Z]+)?\s*(.+)$";

        if let Ok(regex) = regex::Regex::new(pattern) {
            if let Some(captures) = regex.captures(trimmed) {
                let amount_str = captures.get(1).map(|m| m.as_str()).unwrap_or("1");
                let unit = captures.get(2).map(|m| m.as_str()).unwrap_or("unit");
                let name = captures.get(3).map(|m| m.as_str()).unwrap_or(trimmed);

                let amount = amount_str.parse::<f64>().unwrap_or(1.0);

                return Ok(Some(crate::database::Ingredient {
                    name: name.trim().to_string(),
                    amount: amount.to_string(),
                    unit: normalize_unit(unit),
                    category: None,
                    section,
                }));
            }
        }

        // Fallback: treat as ingredient name with amount 1
        Ok(Some(crate::database::Ingredient {
            name: trimmed.to_string(),
            amount: "1".to_string(),
            unit: "unit".to_string(),
            category: None,
            section,
        }))
    }

    /// Parse multiple ingredients in batch
    pub async fn parse_ingredients_batch(
        &self,
        ingredients: &[String],
    ) -> Result<Vec<crate::database::Ingredient>> {
        let mut parsed_ingredients = Vec::new();

        for ingredient_str in ingredients {
            // Check if ingredient has section information (format: [Section Name] ingredient text)
            let (section, ingredient_text) = if let Some(captures) = 
                regex::Regex::new(r"^\[([^\]]+)\]\s*(.+)$")
                    .unwrap()
                    .captures(ingredient_str) 
            {
                (Some(captures[1].to_string()), captures[2].to_string())
            } else {
                (None, ingredient_str.clone())
            };

            if let Ok(Some(ingredient)) = self.parse_ingredient(&ingredient_text, section).await {
                parsed_ingredients.push(ingredient);
            }
        }

        Ok(parsed_ingredients)
    }
}

/// Normalize unit names to standard abbreviations
fn normalize_unit(unit: &str) -> String {
    match unit.to_lowercase().as_str() {
        "cup" | "cups" => "cup".to_string(),
        "tablespoon" | "tablespoons" | "tbsp" => "tbsp".to_string(),
        "teaspoon" | "teaspoons" | "tsp" => "tsp".to_string(),
        "pound" | "pounds" | "lb" => "lb".to_string(),
        "ounce" | "ounces" | "oz" => "oz".to_string(),
        "gram" | "grams" | "g" => "g".to_string(),
        "kilogram" | "kilograms" | "kg" => "kg".to_string(),
        "milliliter" | "milliliters" | "ml" => "ml".to_string(),
        "liter" | "liters" | "l" => "l".to_string(),
        "can" | "cans" => "can".to_string(),
        "package" | "packages" => "package".to_string(),
        "jar" | "jars" => "jar".to_string(),
        "bottle" | "bottles" => "bottle".to_string(),
        "bag" | "bags" => "bag".to_string(),
        "box" | "boxes" => "box".to_string(),
        "piece" | "pieces" => "piece".to_string(),
        "slice" | "slices" => "slice".to_string(),
        "clove" | "cloves" => "clove".to_string(),
        "stalk" | "stalks" => "stalk".to_string(),
        "" => "".to_string(),
        _ => "unit".to_string(),
    }
}

/// Global instance of the ingredient parser
static INGREDIENT_PARSER: std::sync::OnceLock<IngredientParser> = std::sync::OnceLock::new();

/// Get the global ingredient parser instance
pub fn get_ingredient_parser() -> &'static IngredientParser {
    INGREDIENT_PARSER.get_or_init(|| IngredientParser::new())
}

#[cfg(test)]
mod tests;
